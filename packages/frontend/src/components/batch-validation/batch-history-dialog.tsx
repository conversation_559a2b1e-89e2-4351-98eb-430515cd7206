'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  History,
  Package,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  MapPin,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useBatchHistory } from '@/hooks/use-batch-validation';
import { cn } from '@/lib/utils';

interface BatchHistoryDialogProps {
  batchNumber: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BatchHistoryDialog({
  batchNumber,
  open,
  onOpenChange
}: BatchHistoryDialogProps) {
  const { data: batchHistory, isLoading, error, refetch } = useBatchHistory(batchNumber);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'goods_receipt':
        return <Package className="h-4 w-4 text-green-600" />;
      case 'sale':
        return <ShoppingCart className="h-4 w-4 text-blue-600" />;
      case 'adjustment':
        return <TrendingUp className="h-4 w-4 text-orange-600" />;
      default:
        return <History className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActivityLabel = (type: string) => {
    switch (type) {
      case 'goods_receipt':
        return 'Penerimaan Barang';
      case 'sale':
        return 'Penjualan';
      case 'adjustment':
        return 'Penyesuaian Stok';
      case 'inventory_creation':
        return 'Pembuatan Inventori';
      default:
        return type;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Aktif</Badge>;
      case 'depleted':
        return <Badge variant="secondary">Habis</Badge>;
      case 'expired':
        return <Badge variant="destructive">Kedaluwarsa</Badge>;
      case 'recalled':
        return <Badge variant="destructive">Ditarik</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatQuantity = (quantity: number) => {
    return quantity >= 0 ? `+${quantity}` : quantity.toString();
  };

  const getQuantityColor = (quantity: number) => {
    return quantity >= 0 ? 'text-green-600' : 'text-red-600';
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Riwayat Batch {batchNumber}
          </DialogTitle>
          <DialogDescription>
            Riwayat lengkap penggunaan dan pergerakan batch number ini
          </DialogDescription>
        </DialogHeader>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Memuat riwayat batch...</span>
          </div>
        )}

        {error && (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <XCircle className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">Gagal memuat riwayat batch</p>
            <Button onClick={() => refetch()} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </div>
        )}

        {batchHistory && (
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Ringkasan</TabsTrigger>
              <TabsTrigger value="history">Riwayat Aktivitas</TabsTrigger>
              <TabsTrigger value="locations">Lokasi Penyimpanan</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Total Diterima</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600">
                    {batchHistory.totalReceived.toLocaleString('id-ID')}
                  </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingDown className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Total Digunakan</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">
                    {batchHistory.totalUsed.toLocaleString('id-ID')}
                  </p>
                </div>

                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Package className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium">Stok Saat Ini</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-600">
                    {batchHistory.currentStock.toLocaleString('id-ID')}
                  </p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium">Status</span>
                  </div>
                  <div className="mt-2">
                    {getStatusBadge(batchHistory.status)}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Informasi Batch</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Nomor Batch:</span>
                      <span className="font-mono">{batchHistory.batchNumber}</span>
                    </div>
                    {batchHistory.createdAt && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Dibuat:</span>
                        <span>{format(new Date(batchHistory.createdAt), 'dd MMM yyyy', { locale: id })}</span>
                      </div>
                    )}
                    {batchHistory.expiryDate && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Kedaluwarsa:</span>
                        <span className={cn(
                          new Date(batchHistory.expiryDate) <= new Date() ? 'text-red-600' : 'text-green-600'
                        )}>
                          {format(new Date(batchHistory.expiryDate), 'dd MMM yyyy', { locale: id })}
                        </span>
                      </div>
                    )}
                    {batchHistory.lastActivity && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Aktivitas Terakhir:</span>
                        <span>{format(new Date(batchHistory.lastActivity), 'dd MMM yyyy HH:mm', { locale: id })}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Lokasi Penyimpanan</h4>
                  <div className="space-y-1">
                    {batchHistory.storageLocations.length > 0 ? (
                      batchHistory.storageLocations.map((location, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span>{location}</span>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-muted-foreground">Tidak ada lokasi penyimpanan tercatat</p>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="history">
              <ScrollArea className="h-96">
                {batchHistory.usageHistory.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Tanggal</TableHead>
                        <TableHead>Aktivitas</TableHead>
                        <TableHead>Produk</TableHead>
                        <TableHead className="text-right">Kuantitas</TableHead>
                        <TableHead>Referensi</TableHead>
                        <TableHead>Catatan</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {batchHistory.usageHistory.map((activity) => (
                        <TableRow key={activity.id}>
                          <TableCell className="text-sm">
                            {format(new Date(activity.date), 'dd/MM/yyyy HH:mm', { locale: id })}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getActivityIcon(activity.type)}
                              <span className="text-sm">{getActivityLabel(activity.type)}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm">{activity.productName}</TableCell>
                          <TableCell className={cn(
                            "text-right text-sm font-mono",
                            getQuantityColor(activity.quantity)
                          )}>
                            {formatQuantity(activity.quantity)} {activity.unitName}
                          </TableCell>
                          <TableCell className="text-sm">{activity.reference || '-'}</TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {activity.notes || '-'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Belum ada riwayat aktivitas untuk batch ini</p>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="locations">
              <div className="space-y-4">
                {batchHistory.storageLocations.length > 0 ? (
                  <div className="grid gap-4">
                    {batchHistory.storageLocations.map((location, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                        <MapPin className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{location}</p>
                          <p className="text-sm text-muted-foreground">
                            Lokasi penyimpanan aktif
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <MapPin className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Tidak ada informasi lokasi penyimpanan</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </DialogContent>
    </Dialog>
  );
}
