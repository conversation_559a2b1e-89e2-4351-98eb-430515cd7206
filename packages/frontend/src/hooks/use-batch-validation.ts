'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDebounce } from 'use-debounce';
import { useQuery } from '@tanstack/react-query';

interface ValidationResult {
  isValid: boolean;
  message?: string;
  level: 'error' | 'warning' | 'success';
}

interface BatchValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validationLevel: 'BASIC' | 'STANDARD' | 'BPOM_COMPLIANT' | 'CONTROLLED';
  bpomCompliant: boolean;
  uniquenessCheck: {
    isUnique: boolean;
    conflictingBatches?: Array<{
      id: string;
      batchNumber: string;
      productName: string;
      supplierName: string;
      createdAt: Date;
    }>;
  };
  formatValidation: {
    passedRules: string[];
    failedRules: string[];
    recommendedFormat?: string;
  };
}

interface UniquenessResult {
  isUnique: boolean;
  conflicts: Array<{
    source: 'inventory' | 'goods_receipt';
    productName: string;
    supplierName: string;
    createdAt: Date;
  }>;
}

interface BatchHistoryResult {
  batchNumber: string;
  usageHistory: Array<{
    id: string;
    type: 'goods_receipt' | 'inventory_creation' | 'sale' | 'adjustment';
    productName: string;
    quantity: number;
    unitName: string;
    date: Date;
    reference?: string;
    notes?: string;
  }>;
  totalReceived: number;
  totalUsed: number;
  currentStock: number;
  lastActivity: Date | null;
  createdAt: Date | null;
  status: 'active' | 'depleted' | 'expired' | 'recalled';
  expiryDate?: Date;
  storageLocations: string[];
}

interface UseBatchValidationOptions {
  productId?: string;
  supplierId?: string;
  expiryDate?: Date;
  manufacturingDate?: Date;
  debounceMs?: number;
  enableRealTimeValidation?: boolean;
}

export function useBatchValidation(
  batchNumber: string,
  options: UseBatchValidationOptions = {}
) {
  const {
    productId,
    supplierId,
    expiryDate,
    manufacturingDate,
    debounceMs = 500,
    enableRealTimeValidation = true
  } = options;

  const [debouncedBatchNumber] = useDebounce(batchNumber, debounceMs);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Real-time validation query
  const {
    data: realTimeResult,
    isLoading: isRealTimeLoading,
    error: realTimeError
  } = useQuery({
    queryKey: ['batch-validation-realtime', debouncedBatchNumber, productId],
    queryFn: async () => {
      if (!debouncedBatchNumber || !productId) return null;

      const response = await fetch(
        `/api/procurement/batch-validation/real-time?batchNumber=${encodeURIComponent(debouncedBatchNumber)}&productId=${encodeURIComponent(productId)}`
      );

      if (!response.ok) {
        throw new Error('Gagal memvalidasi nomor batch');
      }

      const result = await response.json();
      return result.data as ValidationResult;
    },
    enabled: enableRealTimeValidation && !!debouncedBatchNumber && !!productId,
    staleTime: 30000, // 30 seconds
    retry: 1
  });

  // Comprehensive validation function
  const validateComprehensive = useCallback(async (): Promise<BatchValidationResult | null> => {
    if (!batchNumber || !productId) return null;

    try {
      const response = await fetch('/api/procurement/batch-validation/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          batchNumber,
          productId,
          supplierId,
          expiryDate: expiryDate?.toISOString(),
          manufacturingDate: manufacturingDate?.toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Gagal melakukan validasi komprehensif');
      }

      const result = await response.json();
      return result.data as BatchValidationResult;
    } catch (error) {
      console.error('Comprehensive validation error:', error);
      throw error;
    }
  }, [batchNumber, productId, supplierId, expiryDate, manufacturingDate]);

  // Check uniqueness function
  const checkUniqueness = useCallback(async (): Promise<UniquenessResult | null> => {
    if (!batchNumber || !productId) return null;

    try {
      const response = await fetch(
        `/api/procurement/batch-validation/check-uniqueness?batchNumber=${encodeURIComponent(batchNumber)}&productId=${encodeURIComponent(productId)}${supplierId ? `&supplierId=${encodeURIComponent(supplierId)}` : ''}`
      );

      if (!response.ok) {
        throw new Error('Gagal memeriksa keunikan batch');
      }

      const result = await response.json();
      return result.data as UniquenessResult;
    } catch (error) {
      console.error('Uniqueness check error:', error);
      throw error;
    }
  }, [batchNumber, productId, supplierId]);

  // Get batch history function
  const getBatchHistory = useCallback(async (): Promise<BatchHistoryResult | null> => {
    if (!batchNumber) return null;

    try {
      const response = await fetch(
        `/api/procurement/batch-validation/history/${encodeURIComponent(batchNumber)}`
      );

      if (!response.ok) {
        throw new Error('Gagal mengambil riwayat batch');
      }

      const result = await response.json();
      return result.data as BatchHistoryResult;
    } catch (error) {
      console.error('Batch history error:', error);
      throw error;
    }
  }, [batchNumber]);

  // Update validation result when real-time result changes
  useEffect(() => {
    if (realTimeResult) {
      setValidationResult(realTimeResult);
    } else if (realTimeError) {
      setValidationResult({
        isValid: false,
        message: 'Terjadi kesalahan saat validasi',
        level: 'error'
      });
    }
  }, [realTimeResult, realTimeError]);

  // Format validation helper
  const getFormatSuggestion = useCallback((batchNumber: string): string | null => {
    if (!batchNumber) return null;

    const patterns = {
      CONTROLLED: /^[A-Z]{3}[0-9]{8}[A-Z]{2}$/,
      BPOM_COMPLIANT: /^[A-Z]{2,4}[0-9]{6}[A-Z0-9]{2,6}$/,
      STANDARD: /^[A-Z]{2,4}[0-9]{4,8}[A-Z0-9]{0,8}$/,
      GENERIC: /^GEN[0-9]{6}[A-Z0-9]{2,4}$/,
      IMPORT: /^IMP[A-Z]{2,3}[A-Z0-9]{4,12}$/,
      BASIC: /^[A-Za-z0-9\-_]{3,20}$/
    };

    for (const [format, pattern] of Object.entries(patterns)) {
      if (pattern.test(batchNumber.toUpperCase())) {
        return format;
      }
    }

    return null;
  }, []);

  // Validation status helpers
  const isValid = validationResult?.isValid ?? false;
  const hasWarnings = validationResult?.level === 'warning';
  const hasErrors = validationResult?.level === 'error';
  const isLoading = isRealTimeLoading || isValidating;

  return {
    // Validation state
    validationResult,
    isValid,
    hasWarnings,
    hasErrors,
    isLoading,
    
    // Validation functions
    validateComprehensive,
    checkUniqueness,
    getBatchHistory,
    
    // Helpers
    getFormatSuggestion,
    
    // Raw data
    realTimeResult,
    realTimeError
  };
}

// Hook for batch validation rules
export function useBatchValidationRules() {
  return useQuery({
    queryKey: ['batch-validation-rules'],
    queryFn: async () => {
      const response = await fetch('/api/procurement/batch-validation/rules');
      
      if (!response.ok) {
        throw new Error('Gagal mengambil aturan validasi');
      }

      const result = await response.json();
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for batch history with caching
export function useBatchHistory(batchNumber: string) {
  return useQuery({
    queryKey: ['batch-history', batchNumber],
    queryFn: async () => {
      if (!batchNumber) return null;

      const response = await fetch(
        `/api/procurement/batch-validation/history/${encodeURIComponent(batchNumber)}`
      );

      if (!response.ok) {
        throw new Error('Gagal mengambil riwayat batch');
      }

      const result = await response.json();
      return result.data as BatchHistoryResult;
    },
    enabled: !!batchNumber,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
  });
}
