import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { BatchNumberValidationService, BATCH_VALIDATION_RULES } from '../services/batch-number-validation.service';

/**
 * Custom validator for batch number format validation
 */
@ValidatorConstraint({ name: 'isBatchNumberFormat', async: false })
@Injectable()
export class IsBatchNumberFormatConstraint implements ValidatorConstraintInterface {
  validate(batchNumber: string, _args: ValidationArguments) {
    if (!batchNumber || typeof batchNumber !== 'string') {
      return false;
    }

    // Check against basic format first
    if (!BATCH_VALIDATION_RULES.BASIC.test(batchNumber)) {
      return false;
    }

    // Additional format checks
    if (batchNumber.length < 3 || batchNumber.length > 20) {
      return false;
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Format nomor batch tidak valid. Gunakan 3-20 karakter (huruf, angka, -, _)';
  }
}

/**
 * Custom validator for batch number uniqueness (async)
 */
@ValidatorConstraint({ name: 'isBatchNumberUnique', async: true })
@Injectable()
export class IsBatchNumberUniqueConstraint implements ValidatorConstraintInterface {
  constructor(private batchValidationService: BatchNumberValidationService) { }

  async validate(batchNumber: string, args: ValidationArguments) {
    if (!batchNumber) {
      return true; // Skip validation if no batch number provided
    }

    const object = args.object as any;
    const productId = object.productId;
    const supplierId = object.supplierId;

    if (!productId) {
      return true; // Skip if no product ID available
    }

    try {
      const uniquenessResult = await this.batchValidationService.checkUniqueness(
        batchNumber,
        productId,
        supplierId
      );

      return uniquenessResult.isUnique;
    } catch (error) {
      // If validation service fails, allow the value but log the error
      console.error('Batch number uniqueness validation failed:', error);
      return true;
    }
  }

  defaultMessage(args: ValidationArguments) {
    return 'Nomor batch sudah digunakan untuk produk ini';
  }
}

/**
 * Custom validator for BPOM compliance (async)
 */
@ValidatorConstraint({ name: 'isBPOMCompliant', async: true })
@Injectable()
export class IsBPOMCompliantConstraint implements ValidatorConstraintInterface {
  constructor(private batchValidationService: BatchNumberValidationService) { }

  async validate(batchNumber: string, args: ValidationArguments) {
    if (!batchNumber) {
      return true; // Skip validation if no batch number provided
    }

    const object = args.object as any;
    const productId = object.productId;

    if (!productId) {
      return true; // Skip if no product ID available
    }

    try {
      const validationResult = await this.batchValidationService.validateBatchNumber({
        batchNumber,
        productId,
        supplierId: object.supplierId,
        expiryDate: object.expiryDate ? new Date(object.expiryDate) : undefined,
        manufacturingDate: object.manufacturingDate ? new Date(object.manufacturingDate) : undefined,
      });

      // For BPOM compliance, we only show warnings, not errors
      return validationResult.isValid;
    } catch (error) {
      console.error('BPOM compliance validation failed:', error);
      return true;
    }
  }

  defaultMessage(args: ValidationArguments) {
    return 'Nomor batch tidak sesuai dengan standar BPOM Indonesia';
  }
}

/**
 * Custom validator for date alignment between batch, manufacturing, and expiry dates
 */
@ValidatorConstraint({ name: 'isBatchDateAligned', async: false })
@Injectable()
export class IsBatchDateAlignedConstraint implements ValidatorConstraintInterface {
  validate(batchNumber: string, args: ValidationArguments) {
    if (!batchNumber) {
      return true; // Skip validation if no batch number provided
    }

    const object = args.object as any;
    const expiryDate = object.expiryDate ? new Date(object.expiryDate) : null;
    const manufacturingDate = object.manufacturingDate ? new Date(object.manufacturingDate) : null;

    // Extract date from batch number if it follows BPOM format
    const dateMatch = batchNumber.match(/[A-Z]{2,4}([0-9]{6})/);
    if (!dateMatch) {
      return true; // Skip if batch doesn't contain date
    }

    const batchDateStr = dateMatch[1]; // YYMMDD format
    const year = 2000 + parseInt(batchDateStr.substring(0, 2));
    const month = parseInt(batchDateStr.substring(2, 4)) - 1; // Month is 0-indexed
    const day = parseInt(batchDateStr.substring(4, 6));

    const batchDate = new Date(year, month, day);
    const now = new Date();

    // Batch date should not be in the future
    if (batchDate > now) {
      return false;
    }

    // Compare with manufacturing date if provided
    if (manufacturingDate) {
      const timeDiff = Math.abs(batchDate.getTime() - manufacturingDate.getTime());
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      if (daysDiff > 7) { // Allow 7 days tolerance
        return false;
      }
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Tanggal dalam nomor batch tidak sesuai dengan tanggal produksi atau masa depan';
  }
}

/**
 * Decorator for batch number format validation
 */
export function IsBatchNumberFormat(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsBatchNumberFormatConstraint,
    });
  };
}

/**
 * Decorator for batch number uniqueness validation
 */
export function IsBatchNumberUnique(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsBatchNumberUniqueConstraint,
    });
  };
}

/**
 * Decorator for BPOM compliance validation
 */
export function IsBPOMCompliant(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsBPOMCompliantConstraint,
    });
  };
}

/**
 * Decorator for batch date alignment validation
 */
export function IsBatchDateAligned(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsBatchDateAlignedConstraint,
    });
  };
}

/**
 * Combined decorator for comprehensive batch number validation
 */
export function IsAdvancedBatchNumber(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    // Apply format validation
    IsBatchNumberFormat({
      message: 'Format nomor batch tidak valid (3-20 karakter: huruf, angka, -, _)',
      ...validationOptions,
    })(object, propertyName);

    // Apply uniqueness validation
    IsBatchNumberUnique({
      message: 'Nomor batch sudah digunakan untuk produk ini',
      ...validationOptions,
    })(object, propertyName);

    // Apply date alignment validation
    IsBatchDateAligned({
      message: 'Tanggal dalam nomor batch tidak sesuai',
      ...validationOptions,
    })(object, propertyName);
  };
}
